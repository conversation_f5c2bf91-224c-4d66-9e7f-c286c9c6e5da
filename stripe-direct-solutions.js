require('dotenv').config();
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// ========================================
// DIRECT API SOLUTIONS FOR LIFETIME VOLUME
// ========================================

console.log(`
🎯 ALTERNATIVE SOLUTIONS TO GET LIFETIME VOLUME DIRECTLY
========================================================

Unfortunately, Stripe does NOT provide a single API endpoint to get lifetime total volume directly.
Here are the available alternatives, ranked by efficiency:

1. 🏆 STRIPE SIGMA (SQL Queries) - MOST EFFICIENT
2. 📊 Stripe Reporting API 
3. 🔍 Stripe Search API (Limited)
4. ⚡ Balance API (Current balance only, not lifetime)
5. 🔄 Pagination (What we're currently doing)

Let's explore each option:
`);

// ========================================
// SOLUTION 1: STRIPE SIGMA (RECOMMENDED)
// ========================================
async function explainStripeSigma() {
    console.log(`
🏆 SOLUTION 1: STRIPE SIGMA (MOST EFFICIENT)
============================================

Stripe Sigma allows you to run SQL queries directly on your Stripe data.
This is the ONLY way to get aggregate data without pagination.

EXAMPLE SQL QUERY for lifetime volume:
\`\`\`sql
SELECT 
    account_id,
    SUM(amount) as total_lifetime_volume,
    COUNT(*) as total_transactions
FROM charges 
WHERE 
    status = 'succeeded' 
    AND account_id = 'acct_your_connected_account_id'
\`\`\`

PROS:
✅ Single query returns total volume instantly
✅ No pagination required
✅ Can aggregate across multiple accounts
✅ Supports complex filtering and grouping
✅ Scheduled reports available

CONS:
❌ Requires Stripe Sigma subscription ($100+/month)
❌ Only available via Stripe Dashboard (no direct API)
❌ Learning curve for SQL

HOW TO ACCESS:
1. Enable Stripe Sigma in your Dashboard
2. Go to https://dashboard.stripe.com/sigma
3. Create a new query with the SQL above
4. Schedule it to run automatically

NOTE: This is what major platforms like DoorDash use for aggregate reporting.
`);
}

// ========================================
// SOLUTION 2: STRIPE REPORTING API
// ========================================
async function getReportingOptions(connectedAccountId) {
    console.log(`
📊 SOLUTION 2: STRIPE REPORTING API
===================================

Stripe provides pre-built reports that might contain volume data.
`);

    try {
        // List available report types
        const reportTypes = await stripe.reporting.reportTypes.list();
        
        console.log('Available Report Types:');
        reportTypes.data.forEach(type => {
            console.log(`- ${type.id}: ${type.name}`);
        });

        // Example: Create a balance summary report
        console.log(`
EXAMPLE: Creating a balance summary report...
`);
        
        const reportRun = await stripe.reporting.reportRuns.create({
            report_type: 'balance.summary.1',
            parameters: {
                interval_start: Math.floor(Date.now() / 1000) - (365 * 24 * 60 * 60), // 1 year ago
                interval_end: Math.floor(Date.now() / 1000)
            }
        }, {
            stripeAccount: connectedAccountId
        });

        console.log('Report created:', reportRun.id);
        console.log('Status:', reportRun.status);
        console.log('This report will contain balance changes over time.');
        
        return reportRun;

    } catch (error) {
        console.log('❌ Reporting API Error:', error.message);
        console.log('Note: Some reports require live mode data or specific account types.');
    }
}

// ========================================
// SOLUTION 3: BALANCE API (CURRENT ONLY)
// ========================================
async function getCurrentBalance(connectedAccountId) {
    console.log(`
⚡ SOLUTION 3: BALANCE API (CURRENT BALANCE ONLY)
================================================

The Balance API only shows current available/pending amounts, NOT lifetime totals.
`);

    try {
        const balance = await stripe.balance.retrieve({
            stripeAccount: connectedAccountId
        });

        console.log('Current Balance Information:');
        console.log('Available:', balance.available);
        console.log('Pending:', balance.pending);
        
        // Calculate total current balance
        let totalAvailable = 0;
        balance.available.forEach(bal => {
            totalAvailable += bal.amount;
            console.log(`  ${bal.currency.toUpperCase()}: $${(bal.amount / 100).toFixed(2)}`);
        });

        console.log(`
❌ LIMITATION: This only shows current balance, not lifetime processed volume.
   Current balance = Lifetime volume - Payouts - Fees - Refunds
`);

        return balance;

    } catch (error) {
        console.log('❌ Balance API Error:', error.message);
    }
}

// ========================================
// SOLUTION 4: SEARCH API (LIMITED)
// ========================================
async function searchAPILimitations() {
    console.log(`
🔍 SOLUTION 4: SEARCH API (LIMITED AGGREGATION)
===============================================

The Search API can find transactions but has limitations for aggregation:

PROS:
✅ Faster than full pagination
✅ Supports complex queries
✅ Can filter by date ranges

CONS:
❌ Still requires pagination for large datasets
❌ Limited to 10,000 results max
❌ No built-in SUM/aggregation functions
❌ You still need to calculate totals client-side

EXAMPLE SEARCH QUERY:
\`\`\`javascript
const charges = await stripe.charges.search({
    query: 'status:"succeeded" AND created>2023-01-01',
    limit: 100
});
\`\`\`

This is still not a "direct" solution as you need to sum the results.
`);
}

// ========================================
// MAIN COMPARISON FUNCTION
// ========================================
async function compareAllSolutions() {
    const connectedAccountId = process.env.CONNECTED_ACCOUNT_ID || 'acct_your_connected_account_id';
    
    console.log(`
🔍 TESTING SOLUTIONS FOR ACCOUNT: ${connectedAccountId}
======================================================
`);

    // Explain Stripe Sigma
    await explainStripeSigma();

    // Test Reporting API
    await getReportingOptions(connectedAccountId);

    // Test Balance API
    await getCurrentBalance(connectedAccountId);

    // Explain Search API limitations
    await searchAPILimitations();

    console.log(`
📋 FINAL RECOMMENDATIONS
========================

FOR PRODUCTION USE:
1. 🏆 Use STRIPE SIGMA if you need regular aggregate reporting
   - Cost: $100+/month but saves significant development time
   - Perfect for dashboards and business intelligence

2. 📊 Use REPORTING API for standard reports
   - Free but limited to pre-built report types
   - Good for compliance and accounting needs

3. 🔄 Use PAGINATION (current approach) for one-off calculations
   - Free but slower and more complex
   - Good for occasional calculations

FOR YOUR SPECIFIC CASE:
Since you want "direct API call" for lifetime volume, your options are:

❌ NO direct API endpoint exists for this
✅ Stripe Sigma is the closest thing to "direct" (SQL query)
✅ Reporting API might have relevant pre-built reports
❌ All other methods require pagination/aggregation

BOTTOM LINE:
Stripe intentionally doesn't provide direct aggregate endpoints to:
- Prevent expensive queries on their infrastructure
- Encourage use of their premium Sigma product
- Maintain API performance for real-time operations
`);
}

// ========================================
// STRIPE SIGMA QUERY EXAMPLES
// ========================================
function showSigmaExamples() {
    console.log(`
💡 STRIPE SIGMA QUERY EXAMPLES
==============================

Here are SQL queries you can use in Stripe Sigma:

1. LIFETIME VOLUME FOR ONE ACCOUNT:
\`\`\`sql
SELECT 
    SUM(amount) / 100 as lifetime_volume_usd,
    COUNT(*) as total_transactions,
    MIN(created) as first_transaction,
    MAX(created) as latest_transaction
FROM charges 
WHERE 
    status = 'succeeded' 
    AND account_id = 'acct_your_connected_account_id'
\`\`\`

2. VOLUME BY MONTH:
\`\`\`sql
SELECT 
    DATE_TRUNC('month', created) as month,
    SUM(amount) / 100 as monthly_volume,
    COUNT(*) as monthly_transactions
FROM charges 
WHERE 
    status = 'succeeded' 
    AND account_id = 'acct_your_connected_account_id'
GROUP BY month
ORDER BY month DESC
\`\`\`

3. VOLUME ACROSS ALL CONNECTED ACCOUNTS:
\`\`\`sql
SELECT 
    account_id,
    SUM(amount) / 100 as lifetime_volume,
    COUNT(*) as total_transactions
FROM charges 
WHERE status = 'succeeded'
GROUP BY account_id
ORDER BY lifetime_volume DESC
\`\`\`

To use these:
1. Go to https://dashboard.stripe.com/sigma
2. Click "Create Query"
3. Paste the SQL above
4. Replace 'acct_your_connected_account_id' with your actual account ID
5. Run the query
`);
}

// Run the comparison
if (require.main === module) {
    compareAllSolutions()
        .then(() => showSigmaExamples())
        .catch(console.error);
}

module.exports = {
    explainStripeSigma,
    getReportingOptions,
    getCurrentBalance,
    searchAPILimitations,
    compareAllSolutions,
    showSigmaExamples
};
