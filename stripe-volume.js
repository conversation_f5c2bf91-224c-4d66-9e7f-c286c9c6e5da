require('dotenv').config();
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

async function getConnectedAccountLifetimeVolume(connectedAccountId) {
    let totalVolume = 0;
    let hasMore = true;
    let startingAfter = null;
    let requestCount = 0;
    
    try {
        while (hasMore) {
            if (requestCount > 0 && requestCount % 25 === 0) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            const listParams = {
                limit: 100,
                expand: ['data.source']
            };

            // Only add starting_after if it has a value
            if (startingAfter) {
                listParams.starting_after = startingAfter;
            }

            const balanceTransactions = await stripe.balanceTransactions.list(listParams, {
                stripeAccount: connectedAccountId // This is the key part
            });
            
            balanceTransactions.data.forEach(transaction => {
                if (transaction.type === 'charge' || 
                    transaction.type === 'payment' || 
                    transaction.type === 'payment_intent') {
                    totalVolume += transaction.amount;
                }
            });
            
            hasMore = balanceTransactions.has_more;
            if (hasMore) {
                startingAfter = balanceTransactions.data[balanceTransactions.data.length - 1].id;
            }
            
            requestCount++;
            console.log(`Account ${connectedAccountId} - Processed ${requestCount} batches, current volume: $${(totalVolume / 100).toFixed(2)}`);
        }
        
        return {
            accountId: connectedAccountId,
            totalVolume: totalVolume / 100,
            totalRequests: requestCount
        };
        
    } catch (error) {
        console.error(`Error fetching lifetime volume for account ${connectedAccountId}:`, error);
        throw error;
    }
}

// Example usage
async function main() {
    try {
        // Replace with your actual connected account ID
        const connectedAccountId = process.env.CONNECTED_ACCOUNT_ID || 'acct_your_connected_account_id';
        
        console.log(`Starting to fetch lifetime volume for account: ${connectedAccountId}`);
        const result = await getConnectedAccountLifetimeVolume(connectedAccountId);
        
        console.log('\n=== FINAL RESULT ===');
        console.log(`Account ID: ${result.accountId}`);
        console.log(`Total Lifetime Volume: $${result.totalVolume.toFixed(2)}`);
        console.log(`Total API Requests Made: ${result.totalRequests}`);
        
    } catch (error) {
        console.error('Error in main function:', error);
    }
}

// Run the main function if this file is executed directly
if (require.main === module) {
    main();
}

// Export the function for use in other modules
module.exports = { getConnectedAccountLifetimeVolume };
