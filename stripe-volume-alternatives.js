require('dotenv').config();
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// ========================================
// ALTERNATIVE 1: Using Stripe Search API (Most Efficient)
// ========================================
async function getLifetimeVolumeWithSearch(connectedAccountId) {
    try {
        console.log('🔍 Using Stripe Search API...');
        
        // Search for all successful charges
        const charges = await stripe.charges.search({
            query: 'status:"succeeded"',
            limit: 100
        }, {
            stripeAccount: connectedAccountId
        });
        
        let totalVolume = 0;
        let hasMore = charges.has_more;
        let nextPage = charges.next_page;
        let requestCount = 1;
        
        // Process first batch
        charges.data.forEach(charge => {
            totalVolume += charge.amount;
        });
        
        console.log(`Batch 1: $${(totalVolume / 100).toFixed(2)}`);
        
        // Continue with pagination if needed
        while (hasMore) {
            const nextCharges = await stripe.charges.search({
                query: 'status:"succeeded"',
                limit: 100,
                page: nextPage
            }, {
                stripeAccount: connectedAccountId
            });
            
            nextCharges.data.forEach(charge => {
                totalVolume += charge.amount;
            });
            
            hasMore = nextCharges.has_more;
            nextPage = nextCharges.next_page;
            requestCount++;
            
            console.log(`Batch ${requestCount}: $${(totalVolume / 100).toFixed(2)}`);
        }
        
        return {
            method: 'Search API',
            totalVolume: totalVolume / 100,
            requestCount
        };
        
    } catch (error) {
        console.error('Search API Error:', error.message);
        return null;
    }
}

// ========================================
// ALTERNATIVE 2: Using Payment Intents (Modern Approach)
// ========================================
async function getLifetimeVolumeWithPaymentIntents(connectedAccountId) {
    try {
        console.log('💳 Using Payment Intents API...');
        
        let totalVolume = 0;
        let hasMore = true;
        let startingAfter = null;
        let requestCount = 0;
        
        while (hasMore) {
            const listParams = {
                limit: 100,
                status: 'succeeded'
            };
            
            if (startingAfter) {
                listParams.starting_after = startingAfter;
            }
            
            const paymentIntents = await stripe.paymentIntents.list(listParams, {
                stripeAccount: connectedAccountId
            });
            
            paymentIntents.data.forEach(pi => {
                totalVolume += pi.amount;
            });
            
            hasMore = paymentIntents.has_more;
            if (hasMore) {
                startingAfter = paymentIntents.data[paymentIntents.data.length - 1].id;
            }
            
            requestCount++;
            console.log(`Batch ${requestCount}: $${(totalVolume / 100).toFixed(2)}`);
        }
        
        return {
            method: 'Payment Intents',
            totalVolume: totalVolume / 100,
            requestCount
        };
        
    } catch (error) {
        console.error('Payment Intents Error:', error.message);
        return null;
    }
}

// ========================================
// ALTERNATIVE 3: Using Charges API (Legacy but Fast)
// ========================================
async function getLifetimeVolumeWithCharges(connectedAccountId) {
    try {
        console.log('⚡ Using Charges API...');
        
        let totalVolume = 0;
        let hasMore = true;
        let startingAfter = null;
        let requestCount = 0;
        
        while (hasMore) {
            const listParams = {
                limit: 100
            };
            
            if (startingAfter) {
                listParams.starting_after = startingAfter;
            }
            
            const charges = await stripe.charges.list(listParams, {
                stripeAccount: connectedAccountId
            });
            
            charges.data.forEach(charge => {
                if (charge.paid && !charge.refunded) {
                    totalVolume += charge.amount;
                }
            });
            
            hasMore = charges.has_more;
            if (hasMore) {
                startingAfter = charges.data[charges.data.length - 1].id;
            }
            
            requestCount++;
            console.log(`Batch ${requestCount}: $${(totalVolume / 100).toFixed(2)}`);
        }
        
        return {
            method: 'Charges API',
            totalVolume: totalVolume / 100,
            requestCount
        };
        
    } catch (error) {
        console.error('Charges API Error:', error.message);
        return null;
    }
}

// ========================================
// ALTERNATIVE 4: Using Auto-Pagination (Stripe's Built-in)
// ========================================
async function getLifetimeVolumeWithAutoPagination(connectedAccountId) {
    try {
        console.log('🔄 Using Auto-Pagination...');
        
        let totalVolume = 0;
        let count = 0;
        
        // Use Stripe's auto-pagination feature
        for await (const charge of stripe.charges.list({
            limit: 100
        }, {
            stripeAccount: connectedAccountId
        })) {
            if (charge.paid && !charge.refunded) {
                totalVolume += charge.amount;
            }
            count++;
            
            if (count % 100 === 0) {
                console.log(`Processed ${count} charges: $${(totalVolume / 100).toFixed(2)}`);
            }
        }
        
        return {
            method: 'Auto-Pagination',
            totalVolume: totalVolume / 100,
            totalCharges: count
        };
        
    } catch (error) {
        console.error('Auto-Pagination Error:', error.message);
        return null;
    }
}

// ========================================
// MAIN FUNCTION - Test All Methods
// ========================================
async function compareAllMethods() {
    const connectedAccountId = process.env.CONNECTED_ACCOUNT_ID || 'acct_your_connected_account_id';
    
    console.log(`\n🚀 Testing all methods for account: ${connectedAccountId}\n`);
    
    const methods = [
        { name: 'Search API', func: getLifetimeVolumeWithSearch },
        { name: 'Payment Intents', func: getLifetimeVolumeWithPaymentIntents },
        { name: 'Charges API', func: getLifetimeVolumeWithCharges },
        { name: 'Auto-Pagination', func: getLifetimeVolumeWithAutoPagination }
    ];
    
    const results = [];
    
    for (const method of methods) {
        console.log(`\n${'='.repeat(50)}`);
        console.log(`Testing: ${method.name}`);
        console.log(`${'='.repeat(50)}`);
        
        const startTime = Date.now();
        const result = await method.func(connectedAccountId);
        const endTime = Date.now();
        
        if (result) {
            result.executionTime = endTime - startTime;
            results.push(result);
            console.log(`✅ ${method.name} completed in ${result.executionTime}ms`);
        } else {
            console.log(`❌ ${method.name} failed`);
        }
        
        // Add delay between methods
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Display comparison
    console.log(`\n${'='.repeat(60)}`);
    console.log('📊 COMPARISON RESULTS');
    console.log(`${'='.repeat(60)}`);
    
    results.forEach(result => {
        console.log(`${result.method}:`);
        console.log(`  💰 Total Volume: $${result.totalVolume.toFixed(2)}`);
        console.log(`  📡 API Requests: ${result.requestCount || result.totalCharges}`);
        console.log(`  ⏱️  Execution Time: ${result.executionTime}ms`);
        console.log('');
    });
}

// Run comparison if this file is executed directly
if (require.main === module) {
    compareAllMethods().catch(console.error);
}

module.exports = {
    getLifetimeVolumeWithSearch,
    getLifetimeVolumeWithPaymentIntents,
    getLifetimeVolumeWithCharges,
    getLifetimeVolumeWithAutoPagination,
    compareAllMethods
};
