{"_from": "stripe", "_id": "stripe@18.3.0", "_inBundle": false, "_integrity": "sha512-FkxrTUUcWB4CVN2yzgsfF/YHD6WgYHduaa7VmokCy5TLCgl5UNJkwortxcedrxSavQ8Qfa4Ir4JxcbIYiBsyLg==", "_location": "/stripe", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "stripe", "name": "stripe", "escapedName": "stripe", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/stripe/-/stripe-18.3.0.tgz", "_shasum": "0c3049e4d7542d99bb8e33b584ba75dde4776450", "_spec": "stripe", "_where": "C:\\wamp\\www\\stripe-total-sales", "author": {"name": "Stripe", "email": "<EMAIL>", "url": "https://stripe.com/"}, "bugs": {"url": "https://github.com/stripe/stripe-node/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.askask.com/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "dependencies": {"qs": "^6.11.0"}, "deprecated": false, "description": "Stripe API wrapper", "devDependencies": {"@types/chai": "^4.3.4", "@types/chai-as-promised": "^7.1.5", "@types/mocha": "^10.0.1", "@types/node": ">=12.0.0", "@types/qs": "^6.9.7", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "chai": "^4.3.6", "chai-as-promised": "~7.1.1", "eslint": "^7.32.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^3.4.1", "mocha": "^8.4.0", "mocha-junit-reporter": "^2.1.0", "nock": "^13.2.9", "node-fetch": "^2.6.7", "nyc": "^15.1.0", "prettier": "^1.16.4", "ts-node": "^10.9.1", "typescript": "^4.9.4", "undici-types": "^7.8.0"}, "engines": {"node": ">=12.*"}, "exports": {"types": "./types/index.d.ts", "browser": {"import": "./esm/stripe.esm.worker.js", "require": "./cjs/stripe.cjs.worker.js"}, "bun": {"import": "./esm/stripe.esm.worker.js", "require": "./cjs/stripe.cjs.worker.js"}, "deno": {"import": "./esm/stripe.esm.worker.js", "require": "./cjs/stripe.cjs.worker.js"}, "worker": {"import": "./esm/stripe.esm.worker.js", "require": "./cjs/stripe.cjs.worker.js"}, "workerd": {"import": "./esm/stripe.esm.worker.js", "require": "./cjs/stripe.cjs.worker.js"}, "default": {"import": "./esm/stripe.esm.node.js", "require": "./cjs/stripe.cjs.node.js"}}, "homepage": "https://github.com/stripe/stripe-node", "keywords": ["stripe", "payment processing", "credit cards", "api"], "license": "MIT", "main": "cjs/stripe.cjs.node.js", "name": "stripe", "peerDependencies": {"@types/node": ">=12.x.x"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}, "repository": {"type": "git", "url": "git://github.com/stripe/stripe-node.git"}, "resolutions": {"minimist": "1.2.6", "nanoid": "^3.2.0"}, "scripts": {"prepack": "just install && just build", "test": "tsc -p tsconfig.cjs.json && mocha"}, "types": "types/index.d.ts", "version": "18.3.0"}