{"_from": "es-object-atoms@^1.1.1", "_id": "es-object-atoms@1.1.1", "_inBundle": false, "_integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "_location": "/es-object-atoms", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "es-object-atoms@^1.1.1", "name": "es-object-atoms", "escapedName": "es-object-atoms", "rawSpec": "^1.1.1", "saveSpec": null, "fetchSpec": "^1.1.1"}, "_requiredBy": ["/get-intrinsic", "/get-proto"], "_resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "_shasum": "1c4f2c4837327597ce69d2ca190a7fdd172338c1", "_spec": "es-object-atoms@^1.1.1", "_where": "C:\\wamp\\www\\stripe-total-sales\\node_modules\\get-intrinsic", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/es-object-atoms/issues"}, "bundleDependencies": false, "dependencies": {"es-errors": "^1.3.0"}, "deprecated": false, "description": "ES Object-related atoms: Object, ToObject, RequireObjectCoercible", "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "^8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./RequireObjectCoercible": "./RequireObjectCoercible.js", "./isObject": "./isObject.js", "./ToObject": "./ToObject.js", "./package.json": "./package.json"}, "homepage": "https://github.com/ljharb/es-object-atoms#readme", "keywords": ["javascript", "ecmascript", "object", "toobject", "coercible"], "license": "MIT", "main": "index.js", "name": "es-object-atoms", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-object-atoms.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "posttest": "npx npm@\">= 10.2\" audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "version": "1.1.1"}