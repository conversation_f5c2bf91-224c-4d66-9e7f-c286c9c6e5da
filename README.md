# Stripe Connected Account Lifetime Volume Calculator

This Node.js project calculates the lifetime transaction volume for a Stripe connected account.

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment variables:**
   Edit the `.env` file and replace the placeholder values:
   ```
   STRIPE_SECRET_KEY=sk_test_your_actual_stripe_secret_key
   CONNECTED_ACCOUNT_ID=acct_your_connected_account_id
   ```

3. **Run the application:**
   ```bash
   npm start
   ```
   
   Or directly:
   ```bash
   node stripe-volume.js
   ```

## Features

- Fetches all balance transactions for a connected account
- Calculates total lifetime volume from charges, payments, and payment intents
- Implements rate limiting (1 second delay every 25 requests)
- Provides progress updates during processing
- Handles pagination automatically

## Output

The script will output:
- Progress updates showing batches processed and current volume
- Final results including:
  - Account ID
  - Total lifetime volume in dollars
  - Total number of API requests made

## Important Notes

- Make sure to use your **secret key** (not publishable key)
- The connected account ID should be the account you want to analyze
- The script respects <PERSON><PERSON>'s rate limits
- All amounts are automatically converted from cents to dollars

## Error Handling

The script includes comprehensive error handling and will log detailed error messages if something goes wrong during the API calls.
