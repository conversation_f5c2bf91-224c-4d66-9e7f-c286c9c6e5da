# Stripe Connected Account Lifetime Volume Calculator

This Node.js project provides **multiple approaches** to calculate the lifetime transaction volume for a Stripe connected account, including both pagination-based methods and direct API alternatives.

## 🚀 Quick Start

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment variables:**
   Edit the `.env` file and replace the placeholder values:
   ```
   STRIPE_SECRET_KEY=sk_test_your_actual_stripe_secret_key
   CONNECTED_ACCOUNT_ID=acct_your_connected_account_id
   ```

3. **Choose your approach:**
   ```bash
   npm run optimized    # Recommended: Fastest pagination method
   npm run direct       # Shows all direct API alternatives
   npm run compare      # Compare different pagination methods
   npm start           # Original balance transactions method
   ```

## 📊 Available Methods

### 1. 🏆 Stripe Sigma (MOST EFFICIENT - Recommended)
**Direct SQL queries on Stripe data - No pagination needed!**

```sql
SELECT
    SUM(amount) / 100 as lifetime_volume_usd,
    COUNT(*) as total_transactions
FROM charges
WHERE
    status = 'succeeded'
    AND account_id = 'acct_your_connected_account_id'
```

- ✅ **Instant results** - Single query returns total volume
- ✅ **No API rate limits** - Runs on Stripe's data warehouse
- ✅ **Scheduled reports** - Automate with daily/weekly/monthly runs
- ❌ **Cost**: $100+/month subscription required
- ❌ **Dashboard only** - No direct API access

**How to use:**
1. Enable Stripe Sigma in your Dashboard
2. Go to https://dashboard.stripe.com/sigma
3. Create a new query with the SQL above
4. Replace account ID with your actual connected account ID

### 2. 📈 Reporting API (FREE)
Pre-built reports that may contain volume data:

```bash
npm run direct  # Shows available report types
```

- ✅ **Free** - No additional cost
- ✅ **Structured data** - CSV/JSON exports available
- ❌ **Limited** - Only pre-built report types
- ❌ **May not have exact data** you need

### 3. ⚡ Auto-Pagination (RECOMMENDED FOR API)
Optimized pagination using Stripe's built-in features:

```bash
npm run optimized
```

- ✅ **Most efficient** pagination method
- ✅ **Clean code** - Uses async iteration
- ✅ **Progress tracking** - Real-time updates
- ✅ **Free** - No additional costs
- ❌ **Still requires** multiple API calls for large datasets

### 4. 🔍 Search API (LIMITED)
Faster than full pagination but still requires aggregation:

- ✅ **Faster** than balance transactions
- ✅ **Flexible queries** - Filter by date, status, etc.
- ❌ **10,000 result limit** - May not get all data
- ❌ **Still requires** client-side summation

### 5. 💰 Balance API (CURRENT ONLY)
Shows current balance, not lifetime volume:

```bash
npm run direct  # Demonstrates limitations
```

- ✅ **Instant** - Single API call
- ❌ **Current balance only** - Not lifetime volume
- ❌ **Not useful** for historical totals

## 🎯 Recommendations

### For Production Use:
1. **🏆 Stripe Sigma** - If you need regular reporting and can afford $100+/month
2. **⚡ Auto-Pagination** - If you need occasional calculations and want to stay free
3. **📈 Reporting API** - If pre-built reports meet your needs

### For Your Specific Case:
Since you want a "direct API call" for lifetime volume:

- ❌ **No direct API endpoint exists** for lifetime totals
- ✅ **Stripe Sigma is closest** to "direct" (SQL query)
- ✅ **Reporting API might have** relevant pre-built reports
- ❌ **All other methods require** pagination/aggregation

## 📁 Project Files

- `stripe-volume-optimized.js` - **Recommended**: Most efficient pagination
- `stripe-direct-solutions.js` - Shows all direct API alternatives
- `stripe-volume-alternatives.js` - Compares different pagination methods
- `stripe-volume.js` - Original balance transactions approach

## 🔧 Configuration

Edit `.env` file:
```env
STRIPE_SECRET_KEY=sk_test_your_actual_stripe_secret_key_here
CONNECTED_ACCOUNT_ID=acct_your_connected_account_id_here
```

## 📋 Output Example

```
🚀 Fetching lifetime volume for account: acct_1OmolzQw4MZ76G4z
📊 Processed 500 charges
💰 Current volume: $22,381.61

✅ COMPLETED!
📈 FINAL RESULTS:
   Account ID: acct_1OmolzQw4MZ76G4z
   💰 Total Lifetime Volume: $148,535.72
   📊 Total Charges Processed: 3,400
   ⏱️  Execution Time: 45.2 seconds
```
