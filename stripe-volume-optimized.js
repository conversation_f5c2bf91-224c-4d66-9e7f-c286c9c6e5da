require('dotenv').config();
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// ========================================
// OPTIMIZED APPROACH: Using Stripe's Auto-Pagination
// This is the most efficient and clean approach
// ========================================

async function getLifetimeVolumeOptimized(connectedAccountId) {
    console.log(`🚀 Fetching lifetime volume for account: ${connectedAccountId}`);
    console.log('Using optimized auto-pagination approach...\n');
    
    try {
        let totalVolume = 0;
        let processedCount = 0;
        const startTime = Date.now();
        
        // Use Stripe's built-in auto-pagination with async iteration
        // This handles all the pagination logic automatically
        for await (const charge of stripe.charges.list({
            limit: 100  // Fetch 100 charges per API call
        }, {
            stripeAccount: connectedAccountId
        })) {
            // Only count successful, non-refunded charges
            if (charge.paid && !charge.refunded) {
                totalVolume += charge.amount;
            }
            
            processedCount++;
            
            // Show progress every 500 charges
            if (processedCount % 500 === 0) {
                console.log(`📊 Processed ${processedCount.toLocaleString()} charges`);
                console.log(`💰 Current volume: $${(totalVolume / 100).toLocaleString()}`);
                console.log('');
            }
        }
        
        const endTime = Date.now();
        const executionTime = endTime - startTime;
        
        // Final results
        const result = {
            accountId: connectedAccountId,
            totalVolume: totalVolume / 100,
            totalChargesProcessed: processedCount,
            executionTimeMs: executionTime,
            executionTimeSeconds: (executionTime / 1000).toFixed(2)
        };
        
        console.log('✅ COMPLETED!\n');
        console.log('📈 FINAL RESULTS:');
        console.log(`   Account ID: ${result.accountId}`);
        console.log(`   💰 Total Lifetime Volume: $${result.totalVolume.toLocaleString()}`);
        console.log(`   📊 Total Charges Processed: ${result.totalChargesProcessed.toLocaleString()}`);
        console.log(`   ⏱️  Execution Time: ${result.executionTimeSeconds} seconds`);
        
        return result;
        
    } catch (error) {
        console.error('❌ Error fetching lifetime volume:', error.message);
        
        // Handle specific Stripe errors
        if (error.type === 'StripeAuthenticationError') {
            console.error('🔑 Please check your Stripe secret key in the .env file');
        } else if (error.type === 'StripePermissionError') {
            console.error('🚫 Permission denied. Check if the connected account ID is correct');
        } else if (error.type === 'StripeConnectionError') {
            console.error('🌐 Network connection error. Please check your internet connection');
        }
        
        throw error;
    }
}

// Alternative: Get volume for a specific date range
async function getVolumeForDateRange(connectedAccountId, startDate, endDate) {
    console.log(`📅 Fetching volume from ${startDate} to ${endDate}`);
    
    try {
        let totalVolume = 0;
        let processedCount = 0;
        
        // Convert dates to Unix timestamps
        const startTimestamp = Math.floor(new Date(startDate).getTime() / 1000);
        const endTimestamp = Math.floor(new Date(endDate).getTime() / 1000);
        
        for await (const charge of stripe.charges.list({
            limit: 100,
            created: {
                gte: startTimestamp,
                lte: endTimestamp
            }
        }, {
            stripeAccount: connectedAccountId
        })) {
            if (charge.paid && !charge.refunded) {
                totalVolume += charge.amount;
            }
            processedCount++;
        }
        
        return {
            accountId: connectedAccountId,
            dateRange: `${startDate} to ${endDate}`,
            totalVolume: totalVolume / 100,
            totalChargesProcessed: processedCount
        };
        
    } catch (error) {
        console.error('Error fetching volume for date range:', error.message);
        throw error;
    }
}

// Alternative: Get volume summary by month
async function getVolumeSummaryByMonth(connectedAccountId, year) {
    console.log(`📊 Getting monthly volume summary for ${year}`);
    
    const monthlySummary = [];
    
    for (let month = 0; month < 12; month++) {
        const startDate = new Date(year, month, 1);
        const endDate = new Date(year, month + 1, 0); // Last day of month
        
        try {
            const monthData = await getVolumeForDateRange(
                connectedAccountId,
                startDate.toISOString().split('T')[0],
                endDate.toISOString().split('T')[0]
            );
            
            monthlySummary.push({
                month: startDate.toLocaleString('default', { month: 'long' }),
                volume: monthData.totalVolume,
                charges: monthData.totalChargesProcessed
            });
            
            console.log(`${startDate.toLocaleString('default', { month: 'long' })}: $${monthData.totalVolume.toLocaleString()}`);
            
        } catch (error) {
            console.error(`Error processing ${startDate.toLocaleString('default', { month: 'long' })}:`, error.message);
        }
    }
    
    return monthlySummary;
}

// Main execution
async function main() {
    const connectedAccountId = process.env.CONNECTED_ACCOUNT_ID || 'acct_your_connected_account_id';
    
    try {
        // Get lifetime volume (most common use case)
        await getLifetimeVolumeOptimized(connectedAccountId);
        
        // Uncomment below for additional analysis:
        
        // Get volume for current year
        // const currentYear = new Date().getFullYear();
        // console.log('\n' + '='.repeat(50));
        // await getVolumeSummaryByMonth(connectedAccountId, currentYear);
        
        // Get volume for specific date range
        // console.log('\n' + '='.repeat(50));
        // const rangeResult = await getVolumeForDateRange(
        //     connectedAccountId,
        //     '2024-01-01',
        //     '2024-12-31'
        // );
        // console.log('2024 Volume:', rangeResult);
        
    } catch (error) {
        console.error('Main execution error:', error.message);
        process.exit(1);
    }
}

// Run if executed directly
if (require.main === module) {
    main();
}

module.exports = {
    getLifetimeVolumeOptimized,
    getVolumeForDateRange,
    getVolumeSummaryByMonth
};
